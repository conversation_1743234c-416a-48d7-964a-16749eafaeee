#include <iostream>
#include <thread>
#include <chrono>
#include "../../include/client/ClientGUI.h"

// Forward declaration - we need to include the actual client class
class Client;

// Declare the actual client run function
extern bool runBackupClient();

int main() {
    std::cout << "🔒 Encrypted Backup Client v3.0 - Starting...\n";

    try {
        // Initialize GUI first
        std::cout << "Initializing GUI..." << std::endl;
        if (ClientGUIHelpers::initializeGUI()) {
            std::cout << "GUI initialized successfully!" << std::endl;

            // Update GUI with some test status
            ClientGUIHelpers::updatePhase("🚀 ULTRA MODERN GUI Test Mode");
            ClientGUIHelpers::updateConnectionStatus(false);
            ClientGUIHelpers::updateOperation("GUI Window Test", true, "Enhanced window should be visible");

            // Force the GUI window to be visible
            #ifdef _WIN32
            if (ClientGUI::getInstance()) {
                ClientGUI::getInstance()->showStatusWindow(true);
                std::cout << "🖥️  Enhanced GUI window forced to show!" << std::endl;
            }
            #endif

            std::cout << "🎯 ULTRA MODERN GUI window should now be visible!" << std::endl;
            std::cout << "📱 Look for the 'ULTRA MODERN Backup Client' window on your screen!" << std::endl;
            std::cout << "🔥 Press Enter to start backup client or Ctrl+C to exit..." << std::endl;
            std::cin.get();
        }

        // Run the actual backup client
        if (runBackupClient()) {
            std::cout << "✅ Client completed successfully!\n";

            // Keep GUI open to show success
            std::cout << "Press Enter to exit..." << std::endl;
            std::cin.get();
            return 0;
        } else {
            std::cout << "❌ Client failed or was interrupted.\n";

            // Keep GUI open to show error
            std::cout << "Press Enter to exit..." << std::endl;
            std::cin.get();
            return 1;
        }
    } catch (const std::exception& e) {
        std::cout << "💥 Critical error: " << e.what() << std::endl;
        std::cout << "Press Enter to exit..." << std::endl;
        std::cin.get();
        return 1;
    } catch (...) {
        std::cout << "💥 Unknown critical error occurred.\n";
        std::cout << "Press Enter to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
}
