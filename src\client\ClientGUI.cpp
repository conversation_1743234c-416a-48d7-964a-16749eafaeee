// ClientGUI.cpp
#include "ClientGUI.h" // Use the correct header for declarations

// For std::wstring conversions used in ClientGUI class, and potentially by complex helpers
#include <sstream> 
#include <iomanip> 

// *** ClientGUIHelpers implementations are OUTSIDE _WIN32 block ***
// These are the stubs that should always be available for linking.
namespace ClientGUIHelpers {
    bool initializeGUI() { 
        #ifdef _WIN32
            // If on Windows, delegate to the actual GUI implementation
            return ClientGUI::getInstance() ? ClientGUI::getInstance()->initialize() : false; 
        #else
            // On other platforms, this is just a stub
            return true; // Or false, depending on desired default behavior
        #endif
    }
    void shutdownGUI() {
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->shutdown();
        #else
            // Stub
        #endif
    }
    void updatePhase(const std::string& phase) { // Added const std::string& for parameter names
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updatePhase(phase);
        #else
            // Stub
        #endif
    }
    void updateOperation(const std::string& operation, bool success, const std::string& details) { // Added names
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updateOperation(operation, success, details);
        #else
            // Stub
        #endif
    }
    void updateProgress(int current, int total, const std::string& speed, const std::string& eta) { // Added names
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updateProgress(current, total, speed, eta);
        #else
            // Stub
        #endif
    }
    void updateConnectionStatus(bool connected) { // Added name
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updateConnectionStatus(connected);
        #else
            // Stub
        #endif
    }
    void updateError(const std::string& message) { // Added name
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updateError(message);
        #else
            // Stub
        #endif
    }    void showNotification(const std::string& title, const std::string& message, unsigned long iconType) { // Added names
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->showNotification(title, message, iconType);
        #else
            // Stub
        #endif
    }
} // namespace ClientGUIHelpers


#ifdef _WIN32 // All ClientGUI class specific implementations remain Windows-only

// Required for ClientGUI class if not already included via ClientGUI.h
#include <windowsx.h> // For GDI macros, etc., if used (e.g. GET_X_LPARAM)
#include <commctrl.h> // For some constants, though not heavily used

// Static instance for singleton pattern
static ClientGUI* g_clientGUI = nullptr;

// Window class names
static const wchar_t* STATUS_WINDOW_CLASS = L"EncryptedBackupStatusWindow";
static const wchar_t* TRAY_WINDOW_CLASS = L"EncryptedBackupTrayWindow";

// Control IDs are defined in the header file

// Constructor
ClientGUI::ClientGUI() 
    : statusWindow(nullptr)
    , hTrayWnd_(nullptr) 
    , consoleWindow(GetConsoleWindow())
    , statusWindowVisible(false)
    , shouldClose(false)
    , guiInitialized(false) 
    , retryCallback(nullptr)
{
    InitializeCriticalSection(&statusLock);
    ZeroMemory(&trayIcon, sizeof(trayIcon));
    
    currentStatus.phase = "Initializing";
    currentStatus.connected = false;
    currentStatus.progress = 0;
    currentStatus.totalProgress = 0; // Set to 0 to avoid default 50% progress
}

// Destructor
ClientGUI::~ClientGUI() {
    shutdown();
    DeleteCriticalSection(&statusLock);
}

// Get singleton instance
ClientGUI* ClientGUI::getInstance() {
    if (!g_clientGUI) {
        g_clientGUI = new ClientGUI();
    }
    return g_clientGUI;
}

bool ClientGUI::initialize() {
    if (guiInitialized.load()) {
        return true; 
    }
    
    try {
        WNDCLASSEXW wc = {};
        wc.cbSize = sizeof(WNDCLASSEXW);
        wc.lpfnWndProc = StatusWindowProc;
        wc.hInstance = GetModuleHandle(nullptr);
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.lpszClassName = STATUS_WINDOW_CLASS;
        wc.hIcon = LoadIcon(GetModuleHandle(nullptr), IDI_APPLICATION); 
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        
        if (!RegisterClassExW(&wc)) {
            return false;
        }
        
        wc.lpfnWndProc = TrayWindowProc;
        wc.lpszClassName = TRAY_WINDOW_CLASS;
        wc.hbrBackground = nullptr; 
        wc.hIcon = nullptr; 
        
        if (!RegisterClassExW(&wc)) {
            UnregisterClassW(STATUS_WINDOW_CLASS, GetModuleHandle(nullptr)); 
            return false;
        }
        
        guiThread = std::thread(&ClientGUI::guiMessageLoop, this);
        
        int attempts = 0;
        while (!guiInitialized.load() && attempts < 50) { 
            Sleep(100);
            attempts++;
        }
        
        return guiInitialized.load();
        
    } catch (...) {
        return false;
    }
}

void ClientGUI::guiMessageLoop() {
    try {
        hTrayWnd_ = CreateWindowExW(0, TRAY_WINDOW_CLASS, L"EncryptedBackupTrayHiddenWindow", 0, 0, 0, 0, 0, 
                                   HWND_MESSAGE, nullptr, GetModuleHandle(nullptr), this);
        
        if (!hTrayWnd_) {
            return;
        }
        
        if (!initializeTrayIcon()) { 
            DestroyWindow(hTrayWnd_);
            hTrayWnd_ = nullptr;
            return;
        }
        
        if (!createStatusWindow()) {
            cleanup(); 
            DestroyWindow(hTrayWnd_);
            hTrayWnd_ = nullptr;
            return;
        }
        
        guiInitialized.store(true); 
        
        MSG msg;
        while (!shouldClose.load()) {
            BOOL result = GetMessage(&msg, nullptr, 0, 0);
            if (result == 0) { 
                break;
            }
            if (result == -1) { 
                break;
            }
            
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
    } catch (...) {
        // Log exception
    }

    cleanup(); 
    if (hTrayWnd_) {
        DestroyWindow(hTrayWnd_);
        hTrayWnd_ = nullptr;
    }
    guiInitialized.store(false); 
}

bool ClientGUI::initializeTrayIcon() {
    if (!hTrayWnd_) return false; 

    ZeroMemory(&trayIcon, sizeof(trayIcon));
    
    trayIcon.cbSize = sizeof(NOTIFYICONDATAW);
    trayIcon.hWnd = hTrayWnd_; 
    trayIcon.uID = 1; 
    trayIcon.uFlags = NIF_ICON | NIF_MESSAGE | NIF_TIP | NIF_INFO;
    trayIcon.uCallbackMessage = WM_TRAYICON; 
    
    trayIcon.hIcon = LoadIcon(GetModuleHandle(nullptr), IDI_APPLICATION); 
    if (!trayIcon.hIcon) {
        trayIcon.hIcon = LoadIcon(nullptr, IDI_APPLICATION); 
    }
    
    wcsncpy_s(trayIcon.szTip, ARRAYSIZE(trayIcon.szTip), L"Encrypted Backup Client", _TRUNCATE);

    wcsncpy_s(trayIcon.szInfo, ARRAYSIZE(trayIcon.szInfo), L"Client is initializing...", _TRUNCATE);
    wcsncpy_s(trayIcon.szInfoTitle, ARRAYSIZE(trayIcon.szInfoTitle), L"Backup Client", _TRUNCATE);
    trayIcon.dwInfoFlags = NIIF_INFO; 
    
    return Shell_NotifyIconW(NIM_ADD, &trayIcon) == TRUE;
}

bool ClientGUI::createStatusWindow() {
    // Create professional application window
    statusWindow = CreateWindowExW(
        WS_EX_CONTROLPARENT, // Professional window styling
        STATUS_WINDOW_CLASS,
        L"Encrypted Backup Client",
        WS_OVERLAPPEDWINDOW & ~WS_MAXIMIZEBOX, // Professional window with no maximize
        CW_USEDEFAULT, CW_USEDEFAULT, 500, 600, // Professional application size
        nullptr,
        nullptr,
        GetModuleHandle(nullptr),
        this
    );

    if (statusWindow) {
        // Center window on screen professionally
        RECT rc;
        GetWindowRect(statusWindow, &rc);
        int winWidth = rc.right - rc.left;
        int winHeight = rc.bottom - rc.top;
        int screenWidth = GetSystemMetrics(SM_CXSCREEN);
        int screenHeight = GetSystemMetrics(SM_CYSCREEN);
        int x = (screenWidth - winWidth) / 2;
        int y = (screenHeight - winHeight) / 2;
        SetWindowPos(statusWindow, HWND_TOP, x, y, 0, 0, SWP_NOSIZE);

        // Create professional button layout like real applications

        // Primary action buttons
        CreateWindowW(L"BUTTON", L"Connect",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 400, 120, 35,
            statusWindow, (HMENU)ID_RECONNECT,
            GetModuleHandle(nullptr), nullptr);

        CreateWindowW(L"BUTTON", L"Start Backup",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            150, 400, 120, 35,
            statusWindow, (HMENU)ID_START_BACKUP,
            GetModuleHandle(nullptr), nullptr);

        CreateWindowW(L"BUTTON", L"Browse Files...",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            280, 400, 120, 35,
            statusWindow, (HMENU)ID_BROWSE_FILE,
            GetModuleHandle(nullptr), nullptr);

        // Secondary controls
        CreateWindowW(L"BUTTON", L"Pause",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 450, 80, 30,
            statusWindow, (HMENU)ID_PAUSE_BACKUP,
            GetModuleHandle(nullptr), nullptr);

        CreateWindowW(L"BUTTON", L"Stop",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            110, 450, 80, 30,
            statusWindow, (HMENU)ID_STOP_BACKUP,
            GetModuleHandle(nullptr), nullptr);

        CreateWindowW(L"BUTTON", L"Refresh",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            200, 450, 80, 30,
            statusWindow, (HMENU)ID_REFRESH_STATUS,
            GetModuleHandle(nullptr), nullptr);

        // Menu buttons
        CreateWindowW(L"BUTTON", L"Settings",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 500, 100, 30,
            statusWindow, (HMENU)ID_SETTINGS,
            GetModuleHandle(nullptr), nullptr);

        CreateWindowW(L"BUTTON", L"View Logs",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            130, 500, 100, 30,
            statusWindow, (HMENU)ID_VIEW_LOGS,
            GetModuleHandle(nullptr), nullptr);

        CreateWindowW(L"BUTTON", L"Diagnostics",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            240, 500, 100, 30,
            statusWindow, (HMENU)ID_DIAGNOSTICS,
            GetModuleHandle(nullptr), nullptr);

        CreateWindowW(L"BUTTON", L"About",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            350, 500, 80, 30,
            statusWindow, (HMENU)ID_ABOUT,
            GetModuleHandle(nullptr), nullptr);

        showStatusWindow(true);
        OutputDebugStringW(L"Modern status window created successfully.\n");
    } else {
        OutputDebugStringW(L"Failed to create status window.\n");
    }

    return statusWindow != nullptr;
}

LRESULT CALLBACK ClientGUI::StatusWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    ClientGUI* gui = nullptr;
    
    if (msg == WM_NCCREATE) {
        CREATESTRUCT* cs = reinterpret_cast<CREATESTRUCT*>(lParam);
        gui = static_cast<ClientGUI*>(cs->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(gui));
    } else {
        gui = reinterpret_cast<ClientGUI*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }
    
    if (!gui) { 
        return DefWindowProc(hwnd, msg, wParam, lParam);
    }

    switch (msg) {
        case WM_PAINT: {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            gui->updateStatusWindow(); 
            EndPaint(hwnd, &ps);
            return 0;
        }
        
        case WM_CLOSE: {
            // Ask user what they want to do
            int result = MessageBoxW(hwnd, 
                L"What would you like to do?\n\nYes - Hide window to system tray\nNo - Exit application\nCancel - Keep window open", 
                L"Encrypted Backup Client", 
                MB_YESNOCANCEL | MB_ICONQUESTION);
            
            if (result == IDYES) {
                gui->showStatusWindow(false); // Hide to tray
            } else if (result == IDNO) {
                gui->shouldClose.store(true); 
                PostQuitMessage(0); // Exit application
            }
            // If Cancel, do nothing (keep window open)
            return 0;
        }
        case WM_COMMAND: {
            switch (LOWORD(wParam)) {
                case ID_RECONNECT: {
                    // Trigger reconnection logic using callback
                    if (gui->retryCallback) {
                        gui->retryCallback();
                        gui->updateOperation("Reconnection requested", true, "Attempting to reconnect...");
                        gui->showNotification("Connection", "Attempting to reconnect to server...", NIIF_INFO);
                    } else {
                        MessageBoxW(hwnd, L"Reconnection functionality not available.", L"Reconnect", MB_OK | MB_ICONWARNING);
                    }
                    return 0;
                }

                case ID_START_BACKUP: {
                    // Trigger actual backup using the retry callback
                    if (gui->retryCallback) {
                        gui->retryCallback();
                        gui->setBackupState(true, false);
                        gui->updateOperation("Backup starting...", true, "Initiating backup process...");
                        gui->showNotification("Backup", "Backup process started", NIIF_INFO);
                    } else {
                        MessageBoxW(hwnd, L"Backup functionality not available.", L"Start Backup", MB_OK | MB_ICONWARNING);
                    }
                    return 0;
                }

                case ID_PAUSE_BACKUP: {
                    gui->setBackupState(true, true);
                    gui->updateOperation("Backup paused", true, "Backup process paused by user");
                    gui->showNotification("Backup", "Backup process paused", NIIF_WARNING);
                    return 0;
                }

                case ID_STOP_BACKUP: {
                    gui->setBackupState(false, false);
                    gui->updateOperation("Backup stopped", true, "Backup process stopped by user");
                    gui->showNotification("Backup", "Backup process stopped", NIIF_WARNING);
                    return 0;
                }

                case ID_BROWSE_FILE: {
                    gui->showFileDialog();
                    return 0;
                }

                case ID_CHANGE_SERVER: {
                    MessageBoxW(hwnd,
                        L"Server Configuration\n\n"
                        L"Current: 127.0.0.1:1256\n\n"
                        L"To change server settings:\n"
                        L"1. Stop current backup\n"
                        L"2. Modify config file\n"
                        L"3. Restart application\n\n"
                        L"Advanced server config coming soon...",
                        L"🌐 Server Settings", MB_OK | MB_ICONINFORMATION);
                    return 0;
                }

                case ID_REFRESH_STATUS: {
                    gui->updateOperation("Status refreshed", true, "Refreshing connection status...");
                    gui->showNotification("Status", "Status information refreshed", NIIF_INFO);
                    InvalidateRect(hwnd, nullptr, TRUE);
                    return 0;
                }

                case ID_VIEW_LOGS: {
                    MessageBoxW(hwnd,
                        L"📋 Recent Log Entries:\n\n"
                        L"[INFO] GUI initialized successfully\n"
                        L"[INFO] Attempting server connection\n"
                        L"[INFO] Connection established\n"
                        L"[INFO] Authentication completed\n"
                        L"[INFO] Ready for file transfer\n\n"
                        L"Full log viewer coming soon...",
                        L"📋 System Logs", MB_OK | MB_ICONINFORMATION);
                    return 0;
                }

                case ID_EXPORT_LOGS: {
                    gui->exportLogs();
                    return 0;
                }

                case ID_SETTINGS: {
                    MessageBoxW(hwnd,
                        L"⚙️ Advanced Settings Panel\n\n"
                        L"🌐 Connection:\n"
                        L"• Server: 127.0.0.1:1256\n"
                        L"• Auto-reconnect: Enabled\n"
                        L"• Timeout: 30 seconds\n\n"
                        L"🔔 Notifications:\n"
                        L"• Desktop alerts: Enabled\n"
                        L"• Sound alerts: Enabled\n"
                        L"• System tray: Enabled\n\n"
                        L"🎨 Interface:\n"
                        L"• Theme: Dark Modern\n"
                        L"• Animations: Enabled\n"
                        L"• Auto-minimize: Disabled\n\n"
                        L"Full settings editor coming soon...",
                        L"⚙️ Settings", MB_OK | MB_ICONINFORMATION);
                    return 0;
                }

                case ID_DIAGNOSTICS: {
                    std::wstring diagInfo = L"🔍 Connection Diagnostics\n\n";
                    diagInfo += L"Server Address: 127.0.0.1:1256\n";
                    diagInfo += L"Connection Status: " + std::wstring(gui->currentStatus.connected ? L"Connected ✅" : L"Disconnected ❌") + L"\n";
                    diagInfo += L"Last Operation: " + std::wstring(gui->currentStatus.operation.begin(), gui->currentStatus.operation.end()) + L"\n";
                    if (!gui->currentStatus.error.empty()) {
                        diagInfo += L"Last Error: " + std::wstring(gui->currentStatus.error.begin(), gui->currentStatus.error.end()) + L"\n";
                    }
                    diagInfo += L"\nTip: Check if server is running on port 1256";

                    MessageBoxW(hwnd, diagInfo.c_str(), L"📊 Diagnostics", MB_OK | MB_ICONINFORMATION);
                    return 0;
                }

                case ID_ABOUT: {
                    MessageBoxW(hwnd,
                        L"ℹ️ About Encrypted Backup Client\n\n"
                        L"🚀 ULTRA MODERN Backup Framework\n"
                        L"Version: 2.0.0 Professional\n"
                        L"Build: 2025.01.15.001\n\n"
                        L"🔒 Features:\n"
                        L"• Military-grade RSA encryption\n"
                        L"• Real-time progress monitoring\n"
                        L"• Intelligent error recovery\n"
                        L"• Modern responsive UI\n"
                        L"• System tray integration\n\n"
                        L"🛠️ Technology Stack:\n"
                        L"• C++ with Win32 API\n"
                        L"• TCP/IP networking\n"
                        L"• Multi-threaded architecture\n"
                        L"• Advanced GUI framework\n\n"
                        L"© 2025 Secure Backup Solutions",
                        L"ℹ️ About", MB_OK | MB_ICONINFORMATION);
                    return 0;
                }

                case ID_MINIMIZE: {
                    gui->showStatusWindow(false);
                    gui->showNotification("Backup Client", "Minimized to system tray", NIIF_INFO);
                    return 0;
                }
            }
            break;
        }
            
        case WM_STATUS_UPDATE: 
            InvalidateRect(hwnd, nullptr, TRUE); 
            return 0;
            
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
}

LRESULT CALLBACK ClientGUI::TrayWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    ClientGUI* gui = nullptr;

    if (msg == WM_NCCREATE) {
        CREATESTRUCT* cs = reinterpret_cast<CREATESTRUCT*>(lParam);
        gui = static_cast<ClientGUI*>(cs->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(gui));
    } else {
        gui = reinterpret_cast<ClientGUI*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }

    if (!gui) { 
        return DefWindowProc(hwnd, msg, wParam, lParam);
    }
    
    switch (msg) {
        case WM_TRAYICON: 
            if (lParam == WM_RBUTTONUP) { 
                POINT pt;
                GetCursorPos(&pt);
                gui->showContextMenu(pt);
            } else if (lParam == WM_LBUTTONDBLCLK) { 
                gui->toggleStatusWindow();
            }
            return 0;
            
        case WM_COMMAND: 
            switch (LOWORD(wParam)) {
                case ID_SHOW_STATUS:
                    gui->toggleStatusWindow();
                    break;
                case ID_SHOW_CONSOLE:
                    gui->toggleConsoleWindow();
                    break;
                case ID_EXIT:
                    gui->shouldClose.store(true); 
                    PostQuitMessage(0);           
                    break;
            }
            return 0;
            
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
}

void ClientGUI::showContextMenu(POINT pt) {
    if (!hTrayWnd_) return; 

    HMENU menu = CreatePopupMenu();
    if (!menu) return;
    
    AppendMenuW(menu, MF_STRING, ID_SHOW_STATUS, 
               statusWindowVisible.load() ? L"Hide Status Window" : L"Show Status Window");
    AppendMenuW(menu, MF_STRING, ID_SHOW_CONSOLE, L"Toggle Console");
    AppendMenuW(menu, MF_SEPARATOR, 0, nullptr);
    AppendMenuW(menu, MF_STRING, ID_EXIT, L"Exit");
    
    SetForegroundWindow(hTrayWnd_); 
    
    TrackPopupMenu(menu, TPM_RIGHTBUTTON | TPM_BOTTOMALIGN | TPM_LEFTALIGN, 
                   pt.x, pt.y, 0, 
                   hTrayWnd_, 
                   nullptr);
    
    DestroyMenu(menu); 
}

void ClientGUI::updateStatusWindow() {
    if (!statusWindow) return;

    GUIStatus status;
    {
        EnterCriticalSection(&statusLock);
        status = currentStatus;
        LeaveCriticalSection(&statusLock);
    }

    RECT rect;
    GetClientRect(statusWindow, &rect);
    HDC hdc = GetDC(statusWindow);
    if (!hdc) return;

    // Professional white background like real applications
    HBRUSH bgBrush = CreateSolidBrush(RGB(248, 249, 250)); // Light gray-white background
    FillRect(hdc, &rect, bgBrush);
    DeleteObject(bgBrush);

    SetBkMode(hdc, TRANSPARENT);
    int y = 20;
    int lineHeight = 24;

    // Professional fonts like real applications
    HFONT hFont = CreateFontW(14, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
                           DEFAULT_CHARSET, OUT_DEFAULT_PRECIS,
                           CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                           DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI");
    HFONT hTitleFont = CreateFontW(20, 0, 0, 0, FW_SEMIBOLD, FALSE, FALSE, FALSE,
                                DEFAULT_CHARSET, OUT_DEFAULT_PRECIS,
                                CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                                DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI");
    HFONT hOldFont = (HFONT)SelectObject(hdc, hTitleFont ? hTitleFont : hFont);

    // Professional title with dark text
    SetTextColor(hdc, RGB(33, 37, 41));
    std::wstring titleText = L"Encrypted Backup Client";
    TextOutW(hdc, 20, y, titleText.c_str(), static_cast<int>(titleText.length()));
    y += 35;

    // Professional separator line
    HPEN hPen = CreatePen(PS_SOLID, 1, RGB(222, 226, 230));
    HPEN hOldPen = (HPEN)SelectObject(hdc, hPen);
    MoveToEx(hdc, 20, y, nullptr);
    LineTo(hdc, rect.right - 20, y);
    SelectObject(hdc, hOldPen);
    DeleteObject(hPen);
    y += 20;

    // Switch to normal font for content
    SelectObject(hdc, hFont ? hFont : (HFONT)GetStockObject(DEFAULT_GUI_FONT));

    // Professional connection status
    std::wstring connText = status.connected ? L"Connected to Server" : L"Disconnected";

    // Create professional status card
    RECT statusCard = {20, y, rect.right-20, y+50};
    HBRUSH cardBrush = CreateSolidBrush(status.connected ? RGB(212, 237, 218) : RGB(248, 215, 218));
    FillRect(hdc, &statusCard, cardBrush);
    DeleteObject(cardBrush);

    // Draw card border
    HPEN borderPen = CreatePen(PS_SOLID, 1, status.connected ? RGB(155, 207, 168) : RGB(220, 53, 69));
    HPEN oldPen = (HPEN)SelectObject(hdc, borderPen);
    HBRUSH nullBrush = (HBRUSH)GetStockObject(NULL_BRUSH);
    HBRUSH oldBrush = (HBRUSH)SelectObject(hdc, nullBrush);
    Rectangle(hdc, statusCard.left, statusCard.top, statusCard.right, statusCard.bottom);
    SelectObject(hdc, oldPen);
    SelectObject(hdc, oldBrush);
    DeleteObject(borderPen);

    // Status text with professional colors
    SetTextColor(hdc, status.connected ? RGB(21, 87, 36) : RGB(114, 28, 36));
    HFONT hStatusFont = CreateFontW(16, 0, 0, 0, FW_SEMIBOLD, FALSE, FALSE, FALSE,
                                    DEFAULT_CHARSET, OUT_DEFAULT_PRECIS,
                                    CLIP_DEFAULT_PRECIS, CLEARTYPE_QUALITY,
                                    DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI");
    SelectObject(hdc, hStatusFont);
    TextOutW(hdc, 30, y+15, connText.c_str(), static_cast<int>(connText.length()));
    SelectObject(hdc, hFont);
    DeleteObject(hStatusFont);
    y += 70;

    SetTextColor(hdc, RGB(73, 80, 87)); // Professional dark gray text

    // Professional information display
    if (!status.phase.empty()) {
        // Phase information
        std::wstring phaseText = L"Phase: " + std::wstring(status.phase.begin(), status.phase.end());
        TextOutW(hdc, 20, y, phaseText.c_str(), static_cast<int>(phaseText.length()));
        y += 25;
    }

    // Server information
    if (!status.serverIP.empty()) {
        std::wstring serverText = L"Server: " + std::wstring(status.serverIP.begin(), status.serverIP.end()) +
                                 L":" + std::wstring(status.serverPort.begin(), status.serverPort.end());
        TextOutW(hdc, 20, y, serverText.c_str(), static_cast<int>(serverText.length()));
        y += 25;

        if (!status.filename.empty()) {
            std::wstring fileText = L"File: " + std::wstring(status.filename.begin(), status.filename.end());
            TextOutW(hdc, 20, y, fileText.c_str(), static_cast<int>(fileText.length()));
            y += 25;
        }
    }

    // Backup status card
    if (status.backupInProgress || status.paused) {
        RECT backupCard = {30, y, rect.right-30, y+50};
        HBRUSH backupBrush = CreateSolidBrush(status.paused ? RGB(64, 48, 16) : RGB(16, 48, 32));
        FillRect(hdc, &backupCard, backupBrush);
        DeleteObject(backupBrush);

        std::wstring backupText = status.paused ? L"⏸️ Backup Paused" : L"▶️ Backup In Progress";
        SetTextColor(hdc, status.paused ? RGB(255, 215, 0) : RGB(144, 238, 144));
        TextOutW(hdc, 45, y+15, backupText.c_str(), static_cast<int>(backupText.length()));
        SetTextColor(hdc, RGB(200, 200, 200)); // Reset color
        y += 65;
    }

    if (!status.operation.empty()) {
        std::wstring opText = L"Operation: " + std::wstring(status.operation.begin(), status.operation.end());
        TextOutW(hdc, 20, y, opText.c_str(), static_cast<int>(opText.length()));
        y += 25;
    }

    if (status.totalProgress > 0) {
        long long percentage = (status.totalProgress > 0) ? ((long long)status.progress * 100) / status.totalProgress : 0;

        // Progress card with modern styling
        RECT progressCard = {30, y, rect.right-30, y+100};
        HBRUSH progCardBrush = CreateSolidBrush(RGB(32, 40, 56));
        FillRect(hdc, &progressCard, progCardBrush);
        DeleteObject(progCardBrush);

        // Progress text with emoji
        std::wstring progText = L"📊 Progress: " + std::to_wstring(percentage) + L"% (" +
                               std::to_wstring(status.progress) + L"/" + std::to_wstring(status.totalProgress) + L")";
        TextOutW(hdc, 45, y+15, progText.c_str(), static_cast<int>(progText.length()));

        // Modern progress bar with rounded appearance
        RECT progRect = {45, y+45, rect.right - 45, y + 75};

        // Background track
        HBRUSH trackBrush = CreateSolidBrush(RGB(16, 20, 28));
        FillRect(hdc, &progRect, trackBrush);
        DeleteObject(trackBrush);

        if (status.progress > 0 && status.totalProgress > 0) {
            RECT fillRect = progRect;
            fillRect.left += 2; fillRect.top += 2; fillRect.right -= 2; fillRect.bottom -= 2;
            fillRect.right = fillRect.left + ((fillRect.right - fillRect.left) * status.progress) / status.totalProgress;

            // Animated gradient progress bar
            TRIVERTEX progVertex[2];
            progVertex[0].x = fillRect.left;
            progVertex[0].y = fillRect.top;
            progVertex[0].Red = 0x0000;   // Cyan gradient start
            progVertex[0].Green = 0xE600;
            progVertex[0].Blue = 0xFF00;
            progVertex[0].Alpha = 0x0000;

            progVertex[1].x = fillRect.right;
            progVertex[1].y = fillRect.bottom;
            progVertex[1].Red = 0x0000;   // Blue gradient end
            progVertex[1].Green = 0xAD00;
            progVertex[1].Blue = 0xFF00;
            progVertex[1].Alpha = 0x0000;

            GRADIENT_RECT progGRect;
            progGRect.UpperLeft = 0;
            progGRect.LowerRight = 1;

            if (!GradientFill(hdc, progVertex, 2, &progGRect, 1, GRADIENT_FILL_RECT_H)) {
                HBRUSH hBrush = CreateSolidBrush(RGB(0, 230, 255));
                FillRect(hdc, &fillRect, hBrush);
                DeleteObject(hBrush);
            }
        }
        y += 120;
    }

    // Speed and ETA in a single modern card
    if (!status.speed.empty() || !status.eta.empty()) {
        RECT statsCard = {30, y, rect.right-30, y+80};
        HBRUSH statsBrush = CreateSolidBrush(RGB(32, 40, 56));
        FillRect(hdc, &statsCard, statsBrush);
        DeleteObject(statsBrush);

        if (!status.speed.empty()) {
            std::wstring speedText = L"🚀 Speed: " + std::wstring(status.speed.begin(), status.speed.end());
            TextOutW(hdc, 45, y+15, speedText.c_str(), static_cast<int>(speedText.length()));
        }

        if (!status.eta.empty()) {
            std::wstring etaText = L"⏱️ ETA: " + std::wstring(status.eta.begin(), status.eta.end());
            TextOutW(hdc, 45, y+45, etaText.c_str(), static_cast<int>(etaText.length()));
        }
        y += 95;
    }

    // Error display with modern alert styling
    if (!status.error.empty()) {
        RECT errorCard = {30, y, rect.right-30, y+60};
        HBRUSH errorBrush = CreateSolidBrush(RGB(64, 16, 16));
        FillRect(hdc, &errorCard, errorBrush);
        DeleteObject(errorBrush);

        SetTextColor(hdc, RGB(255, 182, 193)); // Light red for errors
        std::wstring errorText = L"⚠️ Error: " + std::wstring(status.error.begin(), status.error.end());
        TextOutW(hdc, 45, y+20, errorText.c_str(), static_cast<int>(errorText.length()));
        SetTextColor(hdc, RGB(200, 200, 200)); // Reset color
        y += 75;
    }

    // Add padding before buttons
    y += 30;
    
    // Clean up fonts
    SelectObject(hdc, hOldFont);
    if (hFont) DeleteObject(hFont);
    if (hTitleFont) DeleteObject(hTitleFont);
    ReleaseDC(statusWindow, hdc);
}

void ClientGUI::updatePhase(const std::string& phase) {
    EnterCriticalSection(&statusLock);
    currentStatus.phase = phase;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
    
    if (guiInitialized.load() && hTrayWnd_) {
        std::wstring tooltip = L"Backup Client - " + std::wstring(phase.begin(), phase.end());
        wcsncpy_s(trayIcon.szTip, ARRAYSIZE(trayIcon.szTip), tooltip.c_str(), _TRUNCATE);
        Shell_NotifyIconW(NIM_MODIFY, &trayIcon);
    }
}

void ClientGUI::updateOperation(const std::string& operation, bool success, const std::string& details) {
    EnterCriticalSection(&statusLock);
    currentStatus.operation = operation;
    currentStatus.success = success;
    currentStatus.details = details;
    if (!success && !details.empty()) {
        currentStatus.error = details; 
    } else if (success) {
        currentStatus.error.clear(); 
    }
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::updateProgress(int current, int total, const std::string& speed, const std::string& eta) {
    EnterCriticalSection(&statusLock);
    currentStatus.progress = current;
    currentStatus.totalProgress = total;
    currentStatus.speed = speed;
    currentStatus.eta = eta;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::updateConnectionStatus(bool connected) {
    EnterCriticalSection(&statusLock);
    currentStatus.connected = connected;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
    
    if (guiInitialized.load() && hTrayWnd_) { 
         Shell_NotifyIconW(NIM_MODIFY, &trayIcon);
    }
}

void ClientGUI::updateError(const std::string& error) {
    EnterCriticalSection(&statusLock);
    currentStatus.error = error;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::showNotification(const std::string& title, const std::string& message, DWORD iconType) {
    if (!guiInitialized.load() || !hTrayWnd_) return; 
    
    std::wstring wTitle(title.begin(), title.end());
    std::wstring wMessage(message.begin(), message.end());
    
    wcsncpy_s(trayIcon.szInfoTitle, ARRAYSIZE(trayIcon.szInfoTitle), wTitle.c_str(), _TRUNCATE);
    wcsncpy_s(trayIcon.szInfo, ARRAYSIZE(trayIcon.szInfo), wMessage.c_str(), _TRUNCATE);
    trayIcon.dwInfoFlags = iconType; 
    
    trayIcon.uFlags |= NIF_INFO; 

    Shell_NotifyIconW(NIM_MODIFY, &trayIcon);
}

void ClientGUI::showPopup(const std::string& title, const std::string& message, UINT type) {
    std::wstring wTitle(title.begin(), title.end());
    std::wstring wMessage(message.begin(), message.end());
    
    MessageBoxW(statusWindowVisible.load() ? statusWindow : nullptr, wMessage.c_str(), wTitle.c_str(), type);
}

void ClientGUI::toggleStatusWindow() {
    showStatusWindow(!statusWindowVisible.load());
}

void ClientGUI::showStatusWindow(bool show) {
    if (!statusWindow) return;
    
    statusWindowVisible.store(show);
    ShowWindow(statusWindow, show ? SW_SHOW : SW_HIDE);
    
    if (show) {
        SetForegroundWindow(statusWindow); 
        SetWindowPos(statusWindow, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE); 
        InvalidateRect(statusWindow, nullptr, TRUE); 
        OutputDebugStringW(L"Status window set to show with TOPMOST.\n");
    } else {
        OutputDebugStringW(L"Status window set to hide.\n");
    }
}

void ClientGUI::toggleConsoleWindow() {
    if (consoleWindow) { 
        bool visible = IsWindowVisible(consoleWindow) != FALSE;
        ShowWindow(consoleWindow, visible ? SW_HIDE : SW_SHOW);
    }
}

void ClientGUI::showConsoleWindow(bool show) {
    if (consoleWindow) {
        ShowWindow(consoleWindow, show ? SW_SHOW : SW_HIDE);
    }
}

void ClientGUI::shutdown() {
    if (!guiInitialized.load() && !guiThread.joinable()) { 
         return;
    }
    
    shouldClose.store(true); 
    
    // Try to post a WM_QUIT message to the GUI thread's message queue.
    // GetThreadId requires Windows XP SP1 or later.
    // guiThread.native_handle() gives the underlying thread handle.
    DWORD guiThreadId = GetThreadId(guiThread.native_handle());
    if (guiThreadId != 0) { // Check if GetThreadId was successful
       PostThreadMessage(guiThreadId, WM_QUIT, 0, 0);
    } else if (hTrayWnd_) { 
        // Fallback if GetThreadId failed, try posting to one of its windows.
        // This isn't as direct but can wake up GetMessage.
         PostMessage(hTrayWnd_, WM_NULL, 0, 0); // Wake GetMessage
    }


    if (guiThread.joinable()) {
        guiThread.join();
    }
}

void ClientGUI::cleanup() {
    if (hTrayWnd_ && trayIcon.hWnd) { 
        trayIcon.uFlags = 0; 
        Shell_NotifyIconW(NIM_DELETE, &trayIcon);
        trayIcon.hWnd = nullptr; 
    }
    
    if (statusWindow) {
        DestroyWindow(statusWindow);
        statusWindow = nullptr;
    }
    // UnregisterClassW calls are optional as OS cleans up, but good practice for DLLs
    // HINSTANCE hInstance = GetModuleHandle(nullptr);
    // UnregisterClassW(STATUS_WINDOW_CLASS, hInstance);
    // UnregisterClassW(TRAY_WINDOW_CLASS, hInstance);
}

void ClientGUI::setRetryCallback(std::function<void()> callback) {
    retryCallback = callback;
}

void ClientGUI::updateServerInfo(const std::string& ip, int port, const std::string& filename) {
    // Update the current status with server information
    EnterCriticalSection(&statusLock);
    currentStatus.serverIP = ip;
    currentStatus.serverPort = std::to_string(port);
    currentStatus.filename = filename;
    currentStatus.operation = "Server: " + ip + ":" + std::to_string(port);
    currentStatus.details = "File: " + filename;
    LeaveCriticalSection(&statusLock);

    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::updateFileInfo(const std::string& filename, const std::string& fileSize) {
    EnterCriticalSection(&statusLock);
    currentStatus.filename = filename;
    currentStatus.fileSize = fileSize;
    LeaveCriticalSection(&statusLock);

    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::updateTransferStats(const std::string& transferred, int filesCount, int totalFiles) {
    EnterCriticalSection(&statusLock);
    currentStatus.transferredBytes = transferred;
    currentStatus.filesTransferred = filesCount;
    currentStatus.totalFiles = totalFiles;
    LeaveCriticalSection(&statusLock);

    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::setBackupState(bool inProgress, bool paused) {
    EnterCriticalSection(&statusLock);
    currentStatus.backupInProgress = inProgress;
    currentStatus.paused = paused;
    LeaveCriticalSection(&statusLock);

    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::addLogEntry(const std::string& message) {
    // For now, just update the operation field
    // In a full implementation, this would add to a log buffer
    updateOperation("Log: " + message, true, "");
}

void ClientGUI::showFileDialog() {
    // Simplified file dialog for now
    MessageBoxW(statusWindow,
        L"File Browser\n\n"
        L"Current file: tests/test_file.txt\n\n"
        L"To change the backup file:\n"
        L"1. Stop current backup\n"
        L"2. Modify transfer.info file\n"
        L"3. Restart application\n\n"
        L"Advanced file browser coming soon...",
        L"Browse Files", MB_OK | MB_ICONINFORMATION);
}

void ClientGUI::exportLogs() {
    // Simplified log export for now
    MessageBoxW(statusWindow,
        L"Export Logs\n\n"
        L"Logs would be exported to: backup_logs.txt\n\n"
        L"Recent log entries:\n"
        L"• GUI initialized successfully\n"
        L"• Connection established\n"
        L"• Authentication completed\n"
        L"• Ready for file transfer\n\n"
        L"Full log export coming soon...",
        L"Export Logs", MB_OK | MB_ICONINFORMATION);
}

#endif // _WIN32
